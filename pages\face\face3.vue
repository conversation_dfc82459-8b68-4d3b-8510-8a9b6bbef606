<template>
	<view class="container">
		<view class="header">
			<text class="title">人体检测Demo</text>
			<text class="subtitle">基于微信VisionKit</text>
		</view>
		
		<view class="mode-selector">
			<button 
				:class="['mode-btn', { active: mode === 'camera' }]" 
				@click="switchMode('camera')"
			>
				摄像头检测
			</button>
			<button 
				:class="['mode-btn', { active: mode === 'image' }]" 
				@click="switchMode('image')"
			>
				图片检测
			</button>
		</view>
		
		<!-- 摄像头模式 -->
		<view v-if="mode === 'camera'" class="camera-section">
			<camera 
				class="camera"
				device-position="front"
				flash="off"
				@initdone="onCameraReady"
				@error="onCameraError"
			></camera>
			
			<view class="camera-controls">
				<button 
					:class="['control-btn', { active: isDetecting }]"
					@click="toggleDetection"
				>
					{{ isDetecting ? '停止检测' : '开始检测' }}
				</button>
				<button class="control-btn" @click="toggle3D">
					{{ is3DMode ? '关闭3D' : '开启3D' }}
				</button>
			</view>
		</view>
		
		<!-- 图片模式 -->
		<view v-if="mode === 'image'" class="image-section">
			<view class="image-upload">
				<button class="upload-btn" @click="chooseImage">
					选择图片
				</button>
				<image 
					v-if="selectedImage" 
					:src="selectedImage" 
					class="preview-image"
					mode="aspectFit"
				></image>
			</view>
			
			<view class="image-controls">
				<button 
					class="control-btn" 
					@click="detectImageBody"
					:disabled="!selectedImage"
				>
					检测人体
				</button>
				<button class="control-btn" @click="toggle3D">
					{{ is3DMode ? '关闭3D' : '开启3D' }}
				</button>
			</view>
		</view>
		
		<!-- 检测结果显示 -->
		<view class="results-section">
			<view class="result-header">
				<text class="result-title">检测结果</text>
				<text class="result-count">检测到 {{ bodyAnchors.length }} 个人体</text>
			</view>
			
			<scroll-view class="result-list" scroll-y>
				<view 
					v-for="(anchor, index) in bodyAnchors" 
					:key="index"
					class="result-item"
				>
					<view class="anchor-info">
						<text class="anchor-title">人体 {{ index + 1 }}</text>
						<text class="anchor-score">置信度: {{ (anchor.score * 100).toFixed(1) }}%</text>
					</view>
					
					<view class="anchor-details">
						<text class="detail-label">检测框:</text>
						<text class="detail-value">
							x: {{ anchor.origin[0].toFixed(1) }}, 
							y: {{ anchor.origin[1].toFixed(1) }}, 
							w: {{ anchor.size[0].toFixed(1) }}, 
							h: {{ anchor.size[1].toFixed(1) }}
						</text>
					</view>
					
					<view class="anchor-details">
						<text class="detail-label">关键点数量:</text>
						<text class="detail-value">{{ anchor.points.length / 2 }} 个点</text>
					</view>
					
					<view v-if="anchor.points3d" class="anchor-details">
						<text class="detail-label">3D关键点:</text>
						<text class="detail-value">已启用</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 状态信息 -->
		<view class="status-section">
			<text class="status-text">{{ statusText }}</text>
		</view>

		<!-- 隐藏的canvas用于图片处理 -->
		<canvas
			canvas-id="hiddenCanvas"
			style="position: fixed; top: -1000px; left: -1000px; width: 1px; height: 1px;"
		></canvas>
	</view>
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue'
	
	// 响应式数据
	const mode = ref('camera') // 'camera' 或 'image'
	const isDetecting = ref(false)
	const is3DMode = ref(false)
	const selectedImage = ref('')
	const bodyAnchors = ref([])
	const statusText = ref('准备就绪')
	
	// VisionKit session
	let vkSession = null
	
	// 切换模式
	function switchMode(newMode) {
		if (mode.value === newMode) return
		
		// 停止当前检测
		stopDetection()
		mode.value = newMode
		bodyAnchors.value = []
		statusText.value = '准备就绪'
	}
	
	// 摄像头准备就绪
	function onCameraReady() {
		statusText.value = '摄像头已准备就绪'
		console.log('Camera ready')
	}
	
	// 摄像头错误
	function onCameraError(error) {
		statusText.value = '摄像头初始化失败'
		console.error('Camera error:', error)
	}
	
	// 初始化VisionKit会话
	function initVKSession() {
		try {
			const sessionMode = mode.value === 'camera' ? 1 : 2
			
			vkSession = wx.createVKSession({
				track: {
					body: { mode: sessionMode }
				}
			})
			
			// 监听人体检测结果
			vkSession.on('updateAnchors', (anchors) => {
				bodyAnchors.value = anchors.map(anchor => ({
					points: anchor.points,
					origin: anchor.origin,
					size: anchor.size,
					score: anchor.score || 0,
					confidence: anchor.confidence || [],
					points3d: anchor.points3d || null
				}))
				
				if (mode.value === 'camera' && is3DMode.value) {
					// 在摄像头模式下更新3D模式
					vkSession.update3DMode({ open3d: true })
				}
				
				statusText.value = `检测到 ${anchors.length} 个人体`
			})
			
			// 监听人体移除事件（仅摄像头模式）
			vkSession.on('removeAnchors', () => {
				bodyAnchors.value = []
				statusText.value = '未检测到人体'
				console.log('Body anchors removed')
			})
			
			return vkSession
		} catch (error) {
			console.error('Failed to create VK session:', error)
			statusText.value = 'VisionKit初始化失败'
			return null
		}
	}
	
	// 开始/停止检测
	function toggleDetection() {
		if (isDetecting.value) {
			stopDetection()
		} else {
			startDetection()
		}
	}
	
	// 开始检测
	function startDetection() {
		if (mode.value !== 'camera') return
		
		const session = initVKSession()
		if (!session) return
		
		session.start((errno) => {
			if (errno) {
				console.error('VK session start failed:', errno)
				statusText.value = '检测启动失败'
			} else {
				isDetecting.value = true
				statusText.value = '正在检测...'
				console.log('VK session started successfully')
			}
		})
	}
	
	// 停止检测
	function stopDetection() {
		if (vkSession) {
			try {
				vkSession.destroy()
				vkSession = null
			} catch (error) {
				console.error('Failed to destroy VK session:', error)
			}
		}
		isDetecting.value = false
		bodyAnchors.value = []
		statusText.value = '检测已停止'
	}
	
	// 切换3D模式
	function toggle3D() {
		is3DMode.value = !is3DMode.value
		statusText.value = `3D模式已${is3DMode.value ? '开启' : '关闭'}`
	}
	
	// 选择图片
	function chooseImage() {
		uni.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				selectedImage.value = res.tempFilePaths[0]
				statusText.value = '图片已选择，可以开始检测'
				bodyAnchors.value = []
			},
			fail: (error) => {
				console.error('Choose image failed:', error)
				statusText.value = '选择图片失败'
			}
		})
	}
	
	// 检测图片中的人体
	function detectImageBody() {
		if (!selectedImage.value) {
			statusText.value = '请先选择图片'
			return
		}
		
		statusText.value = '正在处理图片...'
		
		// resolved: 获取图片信息并转换为ArrayBuffer
		uni.getImageInfo({
			src: selectedImage.value,
			success: (imageInfo) => {
				// resolved: 这里需要将图片转换为ArrayBuffer格式
				// 由于小程序限制，这里使用canvas来获取图片数据
				convertImageToArrayBuffer(selectedImage.value, imageInfo.width, imageInfo.height)
			},
			fail: (error) => {
				console.error('Get image info failed:', error)
				statusText.value = '获取图片信息失败'
			}
		})
	}
	
	// 将图片转换为ArrayBuffer
	function convertImageToArrayBuffer(imagePath, width, height) {
		const canvas = uni.createCanvasContext('hiddenCanvas')
		
		// resolved: 绘制图片到canvas
		canvas.drawImage(imagePath, 0, 0, width, height)
		canvas.draw(false, () => {
			// resolved: 获取canvas图像数据
			uni.canvasGetImageData({
				canvasId: 'hiddenCanvas',
				x: 0,
				y: 0,
				width: width,
				height: height,
				success: (res) => {
					const frameBuffer = res.data.buffer
					performImageDetection(frameBuffer, width, height)
				},
				fail: (error) => {
					console.error('Get canvas image data failed:', error)
					statusText.value = '图片数据获取失败'
				}
			})
		})
	}
	
	// 执行图片检测
	function performImageDetection(frameBuffer, width, height) {
		const session = initVKSession()
		if (!session) return
		
		session.start((errno) => {
			if (errno) {
				console.error('VK session start failed:', errno)
				statusText.value = '检测启动失败'
			} else {
				// 执行静态图片检测
				session.detectBody({
					frameBuffer: frameBuffer,
					width: width,
					height: height,
					scoreThreshold: 0.5,
					sourceType: 1,
					open3d: is3DMode.value // 是否开启3D检测
				})
				statusText.value = '图片检测完成'
			}
		})
	}
	
	// 生命周期
	onMounted(() => {
		console.log('Face2 page mounted')
	})
	
	onUnmounted(() => {
		stopDetection()
	})
</script>

<style scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
	}
	
	.subtitle {
		font-size: 24rpx;
		color: #666;
		margin-top: 10rpx;
		display: block;
	}
	
	.mode-selector {
		display: flex;
		margin-bottom: 30rpx;
		background-color: #fff;
		border-radius: 10rpx;
		overflow: hidden;
	}
	
	.mode-btn {
		flex: 1;
		padding: 20rpx;
		background-color: #fff;
		border: none;
		font-size: 28rpx;
		color: #666;
	}
	
	.mode-btn.active {
		background-color: #007aff;
		color: #fff;
	}
	
	.camera-section, .image-section {
		margin-bottom: 30rpx;
	}
	
	.camera {
		width: 100%;
		height: 400rpx;
		border-radius: 10rpx;
		background-color: #000;
	}
	
	.camera-controls, .image-controls {
		display: flex;
		gap: 20rpx;
		margin-top: 20rpx;
	}
	
	.control-btn {
		flex: 1;
		padding: 20rpx;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	.control-btn.active {
		background-color: #ff3b30;
	}
	
	.control-btn:disabled {
		background-color: #ccc;
		color: #999;
	}
	
	.image-upload {
		text-align: center;
	}
	
	.upload-btn {
		padding: 30rpx 60rpx;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
		margin-bottom: 20rpx;
	}
	
	.preview-image {
		width: 100%;
		max-height: 400rpx;
		border-radius: 10rpx;
	}
	
	.results-section {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}
	
	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.result-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.result-count {
		font-size: 24rpx;
		color: #666;
	}
	
	.result-list {
		max-height: 400rpx;
	}
	
	.result-item {
		padding: 20rpx;
		margin-bottom: 20rpx;
		background-color: #f9f9f9;
		border-radius: 10rpx;
	}
	
	.anchor-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}
	
	.anchor-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.anchor-score {
		font-size: 24rpx;
		color: #007aff;
	}
	
	.anchor-details {
		margin-bottom: 10rpx;
	}
	
	.detail-label {
		font-size: 24rpx;
		color: #666;
		margin-right: 10rpx;
	}
	
	.detail-value {
		font-size: 24rpx;
		color: #333;
	}
	
	.status-section {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		text-align: center;
	}
	
	.status-text {
		font-size: 28rpx;
		color: #666;
	}
</style>
