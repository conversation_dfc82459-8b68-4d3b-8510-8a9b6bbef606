<template>
	<view class="container">
		<view class="header">
			<text class="title">人体检测Demo</text>
			<text class="subtitle">基于微信VisionKit - 图片检测</text>
		</view>

		<!-- 图片检测区域 -->
		<view class="image-section">
			<view class="image-upload">
				<view class="upload-buttons">
					<button class="upload-btn" @click="chooseImage">
						选择图片
					</button>
					<button class="upload-btn default-btn" @click="useDefaultImage">
						使用默认图片
					</button>
				</view>
				<image
					v-if="selectedImage"
					:src="selectedImage"
					class="preview-image"
					mode="aspectFit"
				></image>
			</view>

			<view class="image-controls">
				<button
					class="control-btn"
					@click="detectImageBody"
					:disabled="!selectedImage"
				>
					检测人体
				</button>
				<button class="control-btn" @click="testVisionKit">
					测试VisionKit
				</button>
				<button class="control-btn" @click="toggle3D">
					{{ is3DMode ? '关闭3D' : '开启3D' }}
				</button>
			</view>
		</view>
		
		<!-- 检测结果显示 -->
		<view class="results-section">
			<view class="result-header">
				<text class="result-title">检测结果</text>
				<text class="result-count">检测到 {{ bodyAnchors.length }} 个人体</text>
			</view>
			
			<scroll-view class="result-list" scroll-y>
				<view 
					v-for="(anchor, index) in bodyAnchors" 
					:key="index"
					class="result-item"
				>
					<view class="anchor-info">
						<text class="anchor-title">人体 {{ index + 1 }}</text>
						<text class="anchor-score">置信度: {{ (anchor.score * 100).toFixed(1) }}%</text>
					</view>
					
					<view class="anchor-details">
						<text class="detail-label">检测框:</text>
						<text class="detail-value">
							x: {{ anchor.origin[0].toFixed(1) }}, 
							y: {{ anchor.origin[1].toFixed(1) }}, 
							w: {{ anchor.size[0].toFixed(1) }}, 
							h: {{ anchor.size[1].toFixed(1) }}
						</text>
					</view>
					
					<view class="anchor-details">
						<text class="detail-label">关键点数量:</text>
						<text class="detail-value">{{ anchor.points.length / 2 }} 个点</text>
					</view>
					
					<view v-if="anchor.points3d" class="anchor-details">
						<text class="detail-label">3D关键点:</text>
						<text class="detail-value">已启用</text>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 状态信息 -->
		<view class="status-section">
			<text class="status-text">{{ statusText }}</text>
		</view>

		<!-- 隐藏的canvas用于图片处理 -->
		<canvas
			canvas-id="hiddenCanvas"
			style="position: fixed; top: -1000px; left: -1000px; width: 588px; height: 453px;"
		></canvas>
	</view>
</template>

<script setup>
	import { ref, onMounted, onUnmounted } from 'vue'

	// 响应式数据
	const is3DMode = ref(false)
	const selectedImage = ref('')
	const bodyAnchors = ref([])
	const statusText = ref('准备就绪')

	// VisionKit session
	let vkSession = null
	
	// 初始化VisionKit会话（图片模式）
	function initVKSession() {
		try {
			console.log('Initializing VK session for image mode')

			// 检查wx.createVKSession是否存在
			if (!wx.createVKSession) {
				console.error('wx.createVKSession is not available')
				statusText.value = 'VisionKit不可用'
				return null
			}

			vkSession = wx.createVKSession({
				track: {
					body: { mode: 2 } // 2 = 图片模式
				}
			})

			if (!vkSession) {
				console.error('Failed to create VK session - returned null')
				statusText.value = 'VisionKit会话创建失败'
				return null
			}

			console.log('VK session created successfully')

			// 监听人体检测结果
			vkSession.on('updateAnchors', (anchors) => {
				console.log('✅ updateAnchors event triggered!')
				console.log('Body detection result:', {
					count: anchors.length,
					anchors: anchors.map(anchor => ({
						score: anchor.score,
						origin: anchor.origin,
						size: anchor.size,
						pointsCount: anchor.points ? anchor.points.length / 2 : 0,
						has3D: !!anchor.points3d
					}))
				})

				bodyAnchors.value = anchors.map(anchor => ({
					points: anchor.points,
					origin: anchor.origin,
					size: anchor.size,
					score: anchor.score || 0,
					confidence: anchor.confidence || [],
					points3d: anchor.points3d || null
				}))

				statusText.value = `检测到 ${anchors.length} 个人体`
			})

			// 添加错误监听
			vkSession.on('error', (error) => {
				console.error('❌ VK session error:', error)
				statusText.value = 'VisionKit检测出错: ' + JSON.stringify(error)
			})

			// 添加其他可能的事件监听
			vkSession.on('removeAnchors', () => {
				console.log('🗑️ removeAnchors event triggered')
			})

			return vkSession
		} catch (error) {
			console.error('Failed to create VK session:', error)
			statusText.value = 'VisionKit初始化失败: ' + error.message
			return null
		}
	}

	// 停止检测
	function stopDetection() {
		if (vkSession) {
			try {
				vkSession.destroy()
				vkSession = null
			} catch (error) {
				console.error('Failed to destroy VK session:', error)
			}
		}
		bodyAnchors.value = []
		statusText.value = '检测已停止'
	}
	
	// 测试VisionKit可用性
	function testVisionKit() {
		console.log('=== VisionKit 可用性测试 ===')
		statusText.value = '正在测试VisionKit...'

		// 1. 检查基础API
		console.log('1. 检查wx.createVKSession:', typeof wx.createVKSession)
		console.log('2. 检查wx.getSystemInfo:', typeof wx.getSystemInfo)

		// 检查网络状态
		wx.getNetworkType({
			success: (res) => {
				console.log('网络状态:', res.networkType)
				if (res.networkType === 'none') {
					console.warn('⚠️ 无网络连接 - VisionKit需要网络')
				}
			}
		})

		// 3. 获取系统信息
		wx.getSystemInfo({
			success: (res) => {
				console.log('3. 系统信息:', {
					platform: res.platform,
					system: res.system,
					version: res.version,
					SDKVersion: res.SDKVersion,
					brand: res.brand,
					model: res.model
				})

				// 4. 尝试创建VK会话
				if (wx.createVKSession) {
					try {
						const testSession = wx.createVKSession({
							track: {
								body: { mode: 2 }
							}
						})

						if (testSession) {
							console.log('4. ✅ VK会话创建成功')
							console.log('5. 会话方法:', Object.getOwnPropertyNames(testSession))
							statusText.value = 'VisionKit可用！系统支持人体检测'

							// 清理测试会话
							try {
								testSession.destroy()
							} catch (e) {
								console.log('测试会话清理:', e.message)
							}
						} else {
							console.log('4. ❌ VK会话创建失败 - 返回null')
							statusText.value = 'VisionKit不可用 - 会话创建失败'
						}
					} catch (error) {
						console.log('4. ❌ VK会话创建异常:', error)
						statusText.value = 'VisionKit异常: ' + error.message
					}
				} else {
					console.log('4. ❌ wx.createVKSession不存在')
					statusText.value = 'VisionKit不可用 - API不存在'
				}
			},
			fail: (error) => {
				console.error('获取系统信息失败:', error)
				statusText.value = '系统信息获取失败'
			}
		})
	}

	// 切换3D模式
	function toggle3D() {
		is3DMode.value = !is3DMode.value
		statusText.value = `3D模式已${is3DMode.value ? '开启' : '关闭'}`
	}
	
	// 选择图片
	function chooseImage() {
		uni.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				selectedImage.value = res.tempFilePaths[0]
				statusText.value = '图片已选择，可以开始检测'
				bodyAnchors.value = []
				console.log('Image selected:', selectedImage.value)
			},
			fail: (error) => {
				console.error('Choose image failed:', error)
				statusText.value = '选择图片失败'
			}
		})
	}

	// 使用默认图片
	function useDefaultImage() {
		selectedImage.value = '/static/images/body.png'
		statusText.value = '已加载默认图片，可以开始检测'
		bodyAnchors.value = []
		console.log('Default image loaded:', selectedImage.value)
	}
	
	// 检测图片中的人体
	function detectImageBody() {
		if (!selectedImage.value) {
			statusText.value = '请先选择图片'
			console.log('No image selected for detection')
			return
		}

		console.log('Starting image body detection for:', selectedImage.value)
		statusText.value = '正在处理图片...'

		// 先清理之前的session
		if (vkSession) {
			try {
				vkSession.destroy()
				vkSession = null
				console.log('Previous VK session destroyed')
			} catch (error) {
				console.error('Error destroying previous session:', error)
			}
		}

		// resolved: 获取图片信息并转换为ArrayBuffer
		uni.getImageInfo({
			src: selectedImage.value,
			success: (imageInfo) => {
				console.log('Image info obtained:', {
					width: imageInfo.width,
					height: imageInfo.height,
					path: imageInfo.path
				})
				// resolved: 这里需要将图片转换为ArrayBuffer格式
				// 由于小程序限制，这里使用canvas来获取图片数据
				convertImageToArrayBuffer(selectedImage.value, imageInfo.width, imageInfo.height)
			},
			fail: (error) => {
				console.error('Get image info failed:', error)
				statusText.value = '获取图片信息失败'
			}
		})
	}
	
	// 将图片转换为ArrayBuffer
	function convertImageToArrayBuffer(imagePath, width, height) {
		console.log('Converting image to ArrayBuffer:', { imagePath, width, height })
		const canvas = uni.createCanvasContext('hiddenCanvas')

		// resolved: 绘制图片到canvas
		canvas.drawImage(imagePath, 0, 0, width, height)
		canvas.draw(false, () => {
			console.log('Canvas draw completed, getting image data...')
			// resolved: 获取canvas图像数据
			uni.canvasGetImageData({
				canvasId: 'hiddenCanvas',
				x: 0,
				y: 0,
				width: width,
				height: height,
				success: (res) => {
					console.log('Canvas image data obtained:', {
						dataLength: res.data.length,
						bufferSize: res.data.buffer.byteLength,
						width: width,
						height: height,
						expectedSize: width * height * 4,
						dataType: typeof res.data,
						isUint8ClampedArray: res.data instanceof Uint8ClampedArray,
						firstFewBytes: Array.from(res.data.slice(0, 10))
					})

					// 验证数据格式
					if (res.data.buffer.byteLength !== width * height * 4) {
						console.error('Buffer size mismatch!', {
							actual: res.data.buffer.byteLength,
							expected: width * height * 4
						})
						statusText.value = '图片数据格式错误'
						return
					}

					// 将ImageData转换为ArrayBuffer
					const frameBuffer = res.data.buffer
					console.log('FrameBuffer details:', {
						byteLength: frameBuffer.byteLength,
						constructor: frameBuffer.constructor.name
					})

					performImageDetection(frameBuffer, width, height)
				},
				fail: (error) => {
					console.error('Get canvas image data failed:', error)
					statusText.value = '图片数据获取失败'
				}
			})
		})
	}
	
	// 执行图片检测
	function performImageDetection(frameBuffer, width, height) {
		console.log('Starting image detection with parameters:', {
			bufferSize: frameBuffer.byteLength,
			width,
			height,
			is3DMode: is3DMode.value
		})

		const session = initVKSession()
		if (!session) {
			console.error('Failed to initialize VK session for image detection')
			statusText.value = 'VK会话初始化失败'
			return
		}

		try {
			session.start((errno) => {
				console.log('VK session start callback, errno:', errno)

				if (errno) {
					console.error('VK session start failed with errno:', errno)
					statusText.value = '检测启动失败，错误码: ' + errno
				} else {
					console.log('VK session started successfully, calling detectBody...')

					// 执行静态图片检测 - 尝试不同的参数格式
					console.log('Trying detectBody with different parameter formats...')

					// 方法1：标准参数格式
					try {
						console.log('Method 1: Standard parameters')
						const detectParams1 = {
							frameBuffer: frameBuffer,
							width: width,
							height: height,
							scoreThreshold: 0.3,
							sourceType: 1,
							open3d: is3DMode.value
						}
						console.log('Detection parameters (method 1):', detectParams1)

						const result1 = session.detectBody(detectParams1)
						console.log('detectBody result (method 1):', result1)

						if (result1 !== undefined) {
							statusText.value = '检测调用完成，等待结果...'
							console.log('detectBody called successfully, waiting for updateAnchors event...')
						} else {
							throw new Error('detectBody returned undefined')
						}

					} catch (error1) {
						console.error('Method 1 failed:', error1)

						// 方法2：简化参数格式
						try {
							console.log('Method 2: Simplified parameters')
							const detectParams2 = {
								frameBuffer: frameBuffer,
								width: width,
								height: height
							}
							console.log('Detection parameters (method 2):', detectParams2)

							const result2 = session.detectBody(detectParams2)
							console.log('detectBody result (method 2):', result2)

							if (result2 !== undefined) {
								statusText.value = '检测调用完成（简化参数），等待结果...'
								console.log('detectBody called successfully with simplified params')
							} else {
								throw new Error('detectBody returned undefined with simplified params')
							}

						} catch (error2) {
							console.error('Method 2 failed:', error2)

							// 方法3：最基本参数
							try {
								console.log('Method 3: Basic parameters only')
								const result3 = session.detectBody({
									frameBuffer: frameBuffer,
									width: width,
									height: height,
									scoreThreshold: 0.5
								})
								console.log('detectBody result (method 3):', result3)

								if (result3 !== undefined) {
									statusText.value = '检测调用完成（基本参数），等待结果...'
									console.log('detectBody called successfully with basic params')
								} else {
									statusText.value = 'detectBody调用失败，所有方法都返回undefined'
									console.error('All detectBody methods returned undefined')
								}

							} catch (error3) {
								console.error('Method 3 failed:', error3)
								statusText.value = 'detectBody调用失败: ' + error3.message
							}
						}
					}

					// 设置超时检查
					setTimeout(() => {
						if (bodyAnchors.value.length === 0) {
							console.warn('⚠️ No detection results after 8 seconds')
							console.warn('⚠️ 可能的原因:')
							console.warn('   1. 图片中没有人体')
							console.warn('   2. 网络连接问题（需要真机测试）')
							console.warn('   3. VisionKit服务不可用')
							console.warn('   4. 参数配置问题')
							statusText.value = '检测超时，可能未检测到人体或需要真机测试'
						}
					}, 8000)

					// 添加额外的调试信息
					console.log('🔍 Detection process summary:')
					console.log('   - VK Session created: ✅')
					console.log('   - Session started: ✅')
					console.log('   - detectBody called: ✅')
					console.log('   - Waiting for updateAnchors event...')
					console.log('   - If no results after 8s, check network/device')
				}
			})
		} catch (startError) {
			console.error('session.start failed:', startError)
			statusText.value = '启动检测失败: ' + startError.message
		}
	}
	
	// 生命周期
	onMounted(() => {
		console.log('Face3 page mounted')
		// 自动加载默认图片
		useDefaultImage()
	})
	
	onUnmounted(() => {
		stopDetection()
	})
</script>

<style scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		margin-bottom: 30rpx;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
	}
	
	.subtitle {
		font-size: 24rpx;
		color: #666;
		margin-top: 10rpx;
		display: block;
	}
	
	.image-section {
		margin-bottom: 30rpx;
	}

	.image-controls {
		display: flex;
		gap: 20rpx;
		margin-top: 20rpx;
	}
	
	.control-btn {
		flex: 1;
		padding: 20rpx;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	.control-btn.active {
		background-color: #ff3b30;
	}
	
	.control-btn:disabled {
		background-color: #ccc;
		color: #999;
	}
	
	.image-upload {
		text-align: center;
	}

	.upload-buttons {
		display: flex;
		gap: 20rpx;
		justify-content: center;
		margin-bottom: 20rpx;
	}

	.upload-btn {
		padding: 30rpx 60rpx;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
		flex: 1;
		max-width: 200rpx;
	}

	.default-btn {
		background-color: #34c759;
	}
	
	.preview-image {
		width: 100%;
		max-height: 400rpx;
		border-radius: 10rpx;
	}
	
	.results-section {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}
	
	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.result-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.result-count {
		font-size: 24rpx;
		color: #666;
	}
	
	.result-list {
		max-height: 400rpx;
	}
	
	.result-item {
		padding: 20rpx;
		margin-bottom: 20rpx;
		background-color: #f9f9f9;
		border-radius: 10rpx;
	}
	
	.anchor-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}
	
	.anchor-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.anchor-score {
		font-size: 24rpx;
		color: #007aff;
	}
	
	.anchor-details {
		margin-bottom: 10rpx;
	}
	
	.detail-label {
		font-size: 24rpx;
		color: #666;
		margin-right: 10rpx;
	}
	
	.detail-value {
		font-size: 24rpx;
		color: #333;
	}
	
	.status-section {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		text-align: center;
	}
	
	.status-text {
		font-size: 28rpx;
		color: #666;
	}
</style>
