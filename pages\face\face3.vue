<template>
	<view class="container">
		<view class="header">
			<text class="title">人体检测测试</text>
		</view>

		<view class="image-section">
			<view class="image-container">
				<image v-if="selectedImage" :src="selectedImage" class="preview-image" mode="aspectFit"></image>
				<view v-else class="placeholder">
					<text>请选择图片</text>
				</view>
			</view>

			<!-- 隐藏的canvas用于图像处理 -->
			<canvas canvas-id="detectionCanvas" class="hidden-canvas"></canvas>
		</view>

		<view class="controls">
			<view class="button-row">
				<button class="control-btn" @click="chooseImage">
					选择图片
				</button>
				<button class="control-btn" @click="useDefaultImage">
					使用默认图片
				</button>
			</view>

			<view class="button-row">
				<button class="control-btn primary" @click="detectBody" :disabled="!selectedImage">
					检测人体
				</button>
			</view>
		</view>

		<view class="status">
			<text class="status-text">{{ statusText }}</text>
		</view>

		<!-- 检测结果 -->
		<view v-if="bodyAnchors.length > 0" class="results">
			<text class="results-title">检测结果：</text>
			<view v-for="(anchor, index) in bodyAnchors" :key="index" class="anchor-item">
				<text>人体 {{ index + 1 }}：{{ anchor.points?.length || 0 }} 个关键点</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue'

	// 响应式数据
	const selectedImage = ref('')
	const statusText = ref('请选择图片开始检测')
	const bodyAnchors = ref([])

	// 选择图片
	function chooseImage() {
		uni.chooseImage({
			count: 1,
			sizeType: ['original', 'compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				selectedImage.value = res.tempFilePaths[0]
				statusText.value = '图片已选择，可以开始检测'
				bodyAnchors.value = []
				console.log('Image selected:', selectedImage.value)
			},
			fail: (error) => {
				console.error('Choose image failed:', error)
				statusText.value = '选择图片失败'
			}
		})
	}

	// 使用默认图片
	function useDefaultImage() {
		selectedImage.value = '/static/images/body2.jpg'
		statusText.value = '已加载默认图片，可以开始检测'
		bodyAnchors.value = []
		console.log('Default image loaded:', selectedImage.value)
	}

	// 检测人体（按照官方文档实现）
	function detectBody() {
		if (!selectedImage.value) {
			statusText.value = '请先选择图片'
			return
		}

		console.log('=== 🚀 开始人体检测 ===')
		statusText.value = '正在检测人体...'
		bodyAnchors.value = []

		try {
			// 按照官方文档创建VK会话
			const session = wx.createVKSession({
				track: {
					body: { mode: 2 } // mode: 2 - 手动传入图像
				}
			})

			if (!session) {
				statusText.value = '❌ 无法创建VK会话'
				return
			}

			console.log('✅ VK会话创建成功')

			// 按照官方文档监听结果
			session.on('updateAnchors', (anchors) => {
				console.log('🎯 检测结果:', anchors)
				if (anchors && anchors.length > 0) {
					// 按照官方文档格式处理数据
					bodyAnchors.value = anchors.map(anchor => ({
						points: anchor.points,    // 关键点坐标
						origin: anchor.origin,    // 识别框起始点坐标
						size: anchor.size,        // 识别框的大小
						score: anchor.score,      // 人体检测框的置信度
						confidence: anchor.confidence // 人体关键点的置信度
					}))

					statusText.value = `✅ 检测成功！发现 ${anchors.length} 个人体`

					// 打印详细信息
					anchors.forEach((anchor, index) => {
						console.log(`人体 ${index + 1}:`, {
							关键点数量: anchor.points?.length || 0,
							检测框起点: anchor.origin,
							检测框大小: anchor.size,
							检测置信度: anchor.score
						})
					})
				} else {
					console.log('⚠️ 未检测到人体')
					statusText.value = '❌ 未检测到人体'
				}

				// 销毁会话
				session.destroy()
			})

			// 监听错误
			session.on('error', (error) => {
				console.error('❌ VK检测错误:', error)
				statusText.value = '❌ 检测出错: ' + error.message
				session.destroy()
			})

			// 启动会话
			session.start((errno) => {
				if (errno) {
					console.error('❌ VK会话启动失败:', errno)
					statusText.value = `❌ 启动失败 (${errno})`
					session.destroy()
					return
				}

				console.log('✅ VK会话启动成功，开始获取图片数据')

				// 获取图片信息
				uni.getImageInfo({
					src: selectedImage.value,
					success: (imageInfo) => {
						console.log('📷 图片信息:', {
							宽度: imageInfo.width,
							高度: imageInfo.height,
							路径: imageInfo.path
						})

						// 创建canvas获取RGBA像素数据
						const canvas = uni.createCanvasContext('detectionCanvas')
						canvas.drawImage(selectedImage.value, 0, 0, imageInfo.width, imageInfo.height)
						canvas.draw(false, () => {
							// 获取图片的RGBA像素数据
							uni.canvasGetImageData({
								canvasId: 'detectionCanvas',
								x: 0,
								y: 0,
								width: imageInfo.width,
								height: imageInfo.height,
								success: (canvasData) => {
									console.log('🎨 RGBA数据获取成功:', {
										数据长度: canvasData.data.length,
										缓冲区大小: canvasData.data.buffer.byteLength,
										图片尺寸: `${imageInfo.width}x${imageInfo.height}`
									})

									// 按照官方文档调用检测
									try {
										const result = session.detectBody({
											frameBuffer: canvasData.data.buffer, // 图片 ArrayBuffer 数据
											width: imageInfo.width,              // 图像宽度
											height: imageInfo.height,            // 图像高度
											scoreThreshold: 0.5,                 // 评分阈值
											sourceType: 1                        // 图片来源，默认为1
										})

										console.log('🔍 detectBody调用结果:', result)
										statusText.value = '检测调用完成，等待结果...'

									} catch (detectError) {
										console.error('❌ detectBody调用失败:', detectError)
										statusText.value = '❌ 检测调用失败: ' + detectError.message
										session.destroy()
									}
								},
								fail: (canvasError) => {
									console.error('❌ Canvas数据获取失败:', canvasError)
									statusText.value = '❌ 图片数据获取失败'
									session.destroy()
								}
							})
						})
					},
					fail: (imageError) => {
						console.error('❌ 图片信息获取失败:', imageError)
						statusText.value = '❌ 图片信息获取失败'
						session.destroy()
					}
				})
			})

			// 设置超时检查
			setTimeout(() => {
				if (bodyAnchors.value.length === 0) {
					console.warn('⚠️ 检测超时，建议真机测试')
					statusText.value = '检测超时，建议真机测试'
				}
			}, 8000)

		} catch (error) {
			console.error('❌ 检测异常:', error)
			statusText.value = '❌ 检测异常: ' + error.message
		}
	}

	onMounted(() => {
		console.log('人体检测页面已加载')
		useDefaultImage()
	})
</script>

<style scoped>
	.container {
		padding: 20px;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		text-align: center;
		margin-bottom: 30px;
	}

	.title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
	}

	.image-section {
		background: white;
		border-radius: 10px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.image-container {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200px;
		border: 2px dashed #ddd;
		border-radius: 8px;
		margin-bottom: 20px;
	}

	.preview-image {
		max-width: 100%;
		max-height: 300px;
		border-radius: 8px;
	}

	.placeholder {
		color: #999;
		font-size: 16px;
	}

	.hidden-canvas {
		position: absolute;
		left: -9999px;
		top: -9999px;
		width: 1px;
		height: 1px;
	}

	.controls {
		background: white;
		border-radius: 10px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.button-row {
		display: flex;
		gap: 10px;
		margin-bottom: 15px;
		flex-wrap: wrap;
	}

	.button-row:last-child {
		margin-bottom: 0;
	}

	.control-btn {
		flex: 1;
		min-width: 120px;
		height: 44px;
		border: none;
		border-radius: 6px;
		background-color: #007aff;
		color: white;
		font-size: 16px;
		cursor: pointer;
	}

	.control-btn:disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.control-btn.primary {
		background-color: #ff6b35;
	}

	.status {
		background: white;
		border-radius: 10px;
		padding: 15px;
		margin-bottom: 20px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.status-text {
		font-size: 16px;
		color: #333;
		text-align: center;
		display: block;
	}

	.results {
		background: white;
		border-radius: 10px;
		padding: 20px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	}

	.results-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
		display: block;
	}

	.anchor-item {
		padding: 10px;
		background-color: #f8f9fa;
		border-radius: 6px;
		margin-bottom: 10px;
	}

	.anchor-item:last-child {
		margin-bottom: 0;
	}
</style>